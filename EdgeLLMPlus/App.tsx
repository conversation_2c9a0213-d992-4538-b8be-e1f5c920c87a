import React, { useState, useRef, useEffect } from "react";
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
  Alert,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Switch,
} from "react-native";

import Markdown from "react-native-markdown-display";

import { initLlama, releaseAllLlama } from "llama.rn"; // Import llama.rn
import { downloadModel } from "./src/api/model"; // Download function
import ProgressBar from "./src/components/ProgressBar"; // Progress bar component
import RNFS from "react-native-fs"; // File system module
import axios from 'axios/dist/browser/axios.cjs';
import * as ort from 'onnxruntime-react-native'; // Import ONNX Runtime
import { NativeModules } from 'react-native';
import LocalTokenizer from './src/utils/LocalTokenizer'; // Local tokenizer with exact Python compatibility
import BundledTokenizer from './src/utils/BundledTokenizer'; // Bundled tokenizer as fallback

// Import the native HNSW module
const { HnswSearchModule } = NativeModules;



type Message = {
  role: "user" | "assistant" | "system";
  content: string;
  thought?: string; // Single thought block
  showThought?: boolean;
  context?: string; // Retrieved context from RAG
  showContext?: boolean; // Toggle to show/hide context
};

type Document = {
  id: string;
  content: string;
  metadata: {
    source: string;
    type: string;
    index?: number;
  };
};

function App(): React.JSX.Element {

  // RAG System States
  const [embeddingSession, setEmbeddingSession] = useState<ort.InferenceSession | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [ragEnabled, setRagEnabled] = useState<boolean>(true);
  const [isLoadingRAG, setIsLoadingRAG] = useState<boolean>(false);
  const [tokenizer, setTokenizer] = useState<any>(null);

  const INITIAL_CONVERSATION: Message[] = [
    {
      role: "system",
      content:
        "This is a conversation between user and assistant, a friendly chatbot. The assistant has access to a knowledge base and will use it to provide accurate information when available.",
    },
  ];
  const [context, setContext] = useState<any>(null);
  const [conversation, setConversation] =
    useState<Message[]>(INITIAL_CONVERSATION);
  const [userInput, setUserInput] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [isDownloading, setIsDownloading] = useState<boolean>(false);
  const [selectedModelFormat, setSelectedModelFormat] = useState<string>("");
  const [selectedGGUF, setSelectedGGUF] = useState<string | null>(null);
  const [availableGGUFs, setAvailableGGUFs] = useState<string[]>([]); // List of .gguf files
  const [currentPage, setCurrentPage] = useState<
    "modelSelection" | "conversation"
  >("modelSelection"); // Navigation state
  const [tokensPerSecond, setTokensPerSecond] = useState<number[]>([]);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);
  const [downloadedModels, setDownloadedModels] = useState<string[]>([]);

  const modelFormats = [
    { label: "Llama-3.2-1B-Instruct" },
    { label: "Qwen2-0.5B-Instruct" },
    { label: "DeepSeek-R1-Distill-Qwen-1.5B" },
    { label: "SmolLM2-1.7B-Instruct" },
    { label: "Phi-2-2.8B" },
    { label: "Phi-3.5-mini-instruct-3.8B" },
    { label: "Phi-4-mini-instruct-3.8B" },
  ];

  const HF_TO_GGUF = {
    "Llama-3.2-1B-Instruct": "medmekk/Llama-3.2-1B-Instruct.GGUF",
    "DeepSeek-R1-Distill-Qwen-1.5B":
      "medmekk/DeepSeek-R1-Distill-Qwen-1.5B.GGUF",
    "Qwen2-0.5B-Instruct": "medmekk/Qwen2.5-0.5B-Instruct.GGUF",
    "SmolLM2-1.7B-Instruct": "medmekk/SmolLM2-1.7B-Instruct.GGUF",
    "Phi-2-2.8B": "TheBloke/phi-2-GGUF",
    "Phi-3.5-mini-instruct-3.8B": "bartowski/Phi-3.5-mini-instruct-GGUF",
    "Phi-4-mini-instruct-3.8B": "bartowski/microsoft_Phi-4-mini-instruct-GGUF",
  };

  // To handle the scroll view
  const scrollViewRef = useRef<ScrollView>(null);
  const scrollPositionRef = useRef(0);
  const contentHeightRef = useRef(0);

  // Toggle RAG system
  const toggleRAG = () => {
    setRagEnabled(previous => !previous);
  };

  // Toggle context view
  const toggleContext = (messageIndex: number) => {
    setConversation((prev) =>
      prev.map((msg, index) =>
        index === messageIndex ? { ...msg, showContext: !msg.showContext } : msg
      )
    );
  };

  // Test the ONNX embedding model
  const testEmbeddingModel = async () => {
    try {
      console.log("Testing ONNX embedding model...");
      if (!embeddingSession) {
        console.log("Embedding session not initialized yet");
        return;
      }

      // Test with multiple queries to check embedding quality
      const testQueries = [
        "Hello world",
        "What is a cryptoasset?",
        "Financial regulations",
        "Blockchain technology"
      ];

      for (const testText of testQueries) {
        console.log(`\n 🧪 Testing embedding for: ${testText}`);
        const embedding = await embedText(testText);

        if (embedding) {
          console.log("✅ Embedding successful!", {
            length: embedding.length,
            firstFew: Array.from(embedding).slice(0, 5),
            norm: Math.sqrt(Array.from(embedding).reduce((sum, val) => sum + val * val, 0))
          });
        } else {
          console.log("❌ Embedding failed - returned null");
        }
      }

      // Test similarity between related terms
      console.log("\n🔍 Testing embedding similarity...");
      const crypto1 = await embedText("cryptocurrency");
      const crypto2 = await embedText("digital asset");
      const unrelated = await embedText("weather forecast");

      if (crypto1 && crypto2 && unrelated) {
        const similarity1 = cosineSimilarity(crypto1, crypto2);
        const similarity2 = cosineSimilarity(crypto1, unrelated);
        console.log(`Similarity crypto1-crypto2: ${similarity1.toFixed(4)}`);
        console.log(`Similarity crypto1-unrelated: ${similarity2.toFixed(4)}`);
        console.log(`Embedding quality: ${similarity1 > similarity2 ? '✅ Good' : '❌ Poor'}`);
      }
    } catch (error) {
      console.error("❌ Embedding test error:", error);
    }
  };

  // Helper function to calculate cosine similarity
  const cosineSimilarity = (a: Float32Array, b: Float32Array): number => {
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  };

  // Add a button or function to test it:
  const testNativeModule = async () => {
    try {
      console.log("Testing native module...");
      console.log("Available native modules:", Object.keys(NativeModules));

      if (!HnswSearchModule) {
        console.error("HnswSearchModule is null - native module registration failed");
        console.log("Checking for alternative module names...");
        const moduleNames = Object.keys(NativeModules);
        const hnswModules = moduleNames.filter(name =>
          name.toLowerCase().includes('hnsw') || name.toLowerCase().includes('search')
        );
        console.log("HNSW-related modules found:", hnswModules);
        return;
      }

      console.log("HnswSearchModule found:", HnswSearchModule);
      console.log("HnswSearchModule methods:", Object.keys(HnswSearchModule));
    } catch (error) {
      console.error("Error testing native module:", error);
    }
  }

  





    // Call this in useEffect before initRAG
    useEffect(() => {
      checkDownloadedModels();

      testNativeModule();

      // Initialize RAG system
      initRAG()
        .then(() => {
          testFileAccess();
          // Test the embedding model after initialization
          setTimeout(() => {
            testEmbeddingModel();
          }, 1000); // Give it a moment to fully initialize
        })
        .catch(error => {
          console.error("Error in initialization:", error);
        });

      return () => {
        // Cleanup
        if (context) {
          releaseAllLlama();
        }
      };
    }, []);

    const testFileAccess = async () => {
      try {
        const indexPath = `${RNFS.DocumentDirectoryPath}/hnsw_rag`;
        const graphPath = `${indexPath}/documents_hnsw_rs.hnsw.graph`;
        const dataPath = `${indexPath}/documents_hnsw_rs.hnsw.data`;
        const metadataPath = `${indexPath}/documents_metadata.json`;

        const graphExists = await RNFS.exists(graphPath);
        const dataExists = await RNFS.exists(dataPath);
        const metadataExists = await RNFS.exists(metadataPath);

        console.log("=== RAG File Status ===");
        console.log("Graph file exists:", graphExists, "Path:", graphPath);
        console.log("Data file exists:", dataExists, "Path:", dataPath);
        console.log("Metadata file exists:", metadataExists, "Path:", metadataPath);

        if (graphExists) {
          const graphStat = await RNFS.stat(graphPath);
          console.log("Graph file stats:", graphStat);
        }
        if (dataExists) {
          const dataStat = await RNFS.stat(dataPath);
          console.log("Data file stats:", dataStat);
        }
        if (metadataExists) {
          const metadataStat = await RNFS.stat(metadataPath);
          console.log("Metadata file stats:", metadataStat);
          console.log(`Metadata file size: ${(metadataStat.size / 1024 / 1024).toFixed(2)} MB`);

          // Try to peek at the first few lines to understand the structure
          try {
            const firstChunk = await RNFS.read(metadataPath, 1000, 0, 'utf8');
            console.log("First 1000 chars of metadata:", firstChunk.substring(0, 200) + "...");
          } catch (peekError) {
            console.log("Could not peek at metadata file:", peekError);
          }
        }
        console.log("======================");
      } catch (error) {
        console.error("Error testing file access:", error);
      }
    };

  // Initialize the RAG system
  const initRAG = async () => {
    setIsLoadingRAG(true);
    try {
      // Define paths
      const modelAssetPath = "all-MiniLM-L6-v2.quant.onnx";
      const modelPath = `${RNFS.DocumentDirectoryPath}/all-MiniLM-L6-v2.quant.onnx`;
      const indexAssetPathData = "documents_hnsw_rs.hnsw.data";
      const indexPathData = `${RNFS.DocumentDirectoryPath}/hnsw_rag/documents_hnsw_rs.hnsw.data`;
      const indexAssetPathGraph = "documents_hnsw_rs.hnsw.graph";
      const indexPathGraph = `${RNFS.DocumentDirectoryPath}/hnsw_rag/documents_hnsw_rs.hnsw.graph`;
      const metadataAssetPath = "documents_metadata.json";
      const metadataPath = `${RNFS.DocumentDirectoryPath}/hnsw_rag/documents_metadata.json`;

      // Create directory if needed
      await RNFS.mkdir(`${RNFS.DocumentDirectoryPath}/hnsw_rag`);

      // For iOS, use the bundled resources instead of copyFileAssets
      const copyAssetIfNeeded = async (assetPath, destPath) => {
        const exists = await RNFS.exists(destPath);
        if (!exists) {
          if (Platform.OS === 'ios') {
            // For iOS, use the main bundle path
            const mainBundlePath = `${RNFS.MainBundlePath}/${assetPath}`;
            const fileExists = await RNFS.exists(mainBundlePath);

            if (fileExists) {
              await RNFS.copyFile(mainBundlePath, destPath);
              console.log(`Copied from bundle: ${mainBundlePath} to ${destPath}`);
            } else {
              console.error(`File not found in main bundle: ${mainBundlePath}`);
              throw new Error(`Asset not found: ${assetPath}`);
            }
          } else {
            // For Android, use copyFileAssets
            await RNFS.copyFileAssets(assetPath, destPath);
            console.log(`Copied asset: ${assetPath} to ${destPath}`);
          }
        } else {
          console.log(`File already exists at: ${destPath}`);
        }
      };

      // Copy the required files
      try {
        await copyAssetIfNeeded(modelAssetPath, modelPath);
        await copyAssetIfNeeded(indexAssetPathData, indexPathData);
        await copyAssetIfNeeded(indexAssetPathGraph, indexPathGraph);
        await copyAssetIfNeeded(metadataAssetPath, metadataPath);
      } catch (error) {
        console.error("Error copying assets:", error);
        Alert.alert("Asset Error", "Failed to copy required asset files. RAG features will be disabled.");
        setRagEnabled(false);
        setIsLoadingRAG(false);
        return;
      }

      // Create ONNX session
      console.log("Creating ONNX session from:", modelPath);
      const session = await ort.InferenceSession.create(modelPath);
      setEmbeddingSession(session);
      console.log("ONNX session created successfully");

      // Try to load the local tokenizer first (best compatibility)
      console.log("🔤 Loading local tokenizer (matches Python script exactly)...");
      try {
        const localTokenizer = new LocalTokenizer();
        const success = await localTokenizer.loadFromAssets();

        if (success) {
          setTokenizer(localTokenizer);
          console.log("✅ Local tokenizer loaded successfully");

          // Test the tokenizer to verify it works
          await localTokenizer.test();
          return; // Success, exit early
        } else {
          console.warn("⚠️ Local tokenizer failed to load, trying bundled tokenizer...");
        }
      } catch (tokenizerError) {
        console.error("Local tokenizer error:", tokenizerError);
        console.log("⚠️ Trying bundled tokenizer...");
      }

      // Fallback to bundled tokenizer
      try {
        const bundledTokenizer = new BundledTokenizer();
        setTokenizer(bundledTokenizer);
        console.log("✅ Bundled tokenizer loaded successfully");

        // Test the bundled tokenizer
        await bundledTokenizer.test();
      } catch (bundledError) {
        console.error("Bundled tokenizer error:", bundledError);
        console.log("❌ All tokenizers failed, using basic fallback");
        setTokenizer(null);
      }

      // Load the HNSW index and documents
      await loadHnswIndex();

      console.log("RAG system initialized successfully");
    } catch (error) {
      console.error("Failed to initialize RAG system:", error);
      Alert.alert("RAG Error", "Failed to initialize the RAG system. Some features may be limited.");
      setRagEnabled(false);
    } finally {
      setIsLoadingRAG(false);
    }
  };

// Cache for loaded documents to avoid re-reading
const documentCache = new Map<number, Document>();

// Clear document cache when needed
const clearDocumentCache = () => {
  documentCache.clear();
  console.log("📄 Document cache cleared");
};

// Lazy document loader - loads documents on demand with caching
const loadDocumentByIndex = async (index: number): Promise<Document | null> => {
  // Check cache first
  if (documentCache.has(index)) {
    console.log(`💾 Cache hit for document ${index}`);
    return documentCache.get(index)!;
  }

  try {
    const indexPath = `${RNFS.DocumentDirectoryPath}/hnsw_rag`;
    const metadataPath = `${indexPath}/documents_metadata.json`;

    // Calculate approximate position in file (this is a rough estimate)
    // Each document is roughly the same size, so we can estimate position
    const chunkSize = 1024 * 1024; // 1MB chunks
    const estimatedDocSize = 1000; // Rough estimate of average document size in bytes
    const estimatedPosition = index * estimatedDocSize;

    // Read a chunk around the estimated position
    let position = Math.max(0, estimatedPosition - chunkSize / 2);
    const chunk = await RNFS.read(metadataPath, chunkSize, position, 'utf8');

    // Parse documents from the chunk and cache them
    const documents = [];
    let currentDoc = '';
    let braceCount = 0;
    let inDocument = false;

    for (let i = 0; i < chunk.length; i++) {
      const char = chunk[i];

      if (char === '{' && !inDocument) {
        inDocument = true;
        currentDoc = '{';
        braceCount = 1;
      } else if (inDocument) {
        currentDoc += char;

        if (char === '{') {
          braceCount++;
        } else if (char === '}') {
          braceCount--;

          if (braceCount === 0) {
            try {
              const doc = JSON.parse(currentDoc);
              if (doc.id !== undefined && doc.content && doc.metadata) {
                documents.push(doc);

                // The HNSW index uses numeric indices (0,1,2...) but metadata has string IDs ("doc_0", "doc_1"...)
                // Extract the numeric part from the string ID
                const numericId = parseInt(doc.id.replace('doc_', ''));

                // Cache this document using the numeric ID
                documentCache.set(numericId, doc);

                if (numericId === index) {
                  console.log(`🎯 Found exact document ${index} (ID: ${doc.id}) in chunk`);
                  return doc; // Found the exact document we need
                }
              }
            } catch (parseError) {
              // Skip malformed documents
            }
            inDocument = false;
            currentDoc = '';
          }
        }
      }
    }

    // If we didn't find the exact document, return the closest one by index
    if (documents.length > 0) {
      // Find document with closest numeric ID to the requested index
      const closest = documents.reduce((prev, curr) => {
        const prevNumeric = parseInt(prev.id.replace('doc_', ''));
        const currNumeric = parseInt(curr.id.replace('doc_', ''));
        return Math.abs(currNumeric - index) < Math.abs(prevNumeric - index) ? curr : prev;
      });
      const closestNumeric = parseInt(closest.id.replace('doc_', ''));
      console.log(`📍 Using closest document ${closestNumeric} (ID: ${closest.id}) for requested ${index}`);
      return closest;
    }

    console.warn(`❌ No documents found in chunk for index ${index}`);
    return null;
  } catch (error) {
    console.error(`Error loading document ${index}:`, error);
    return null;
  }
};

// Load the HNSW index (simplified - no metadata loading)
const loadHnswIndex = async () => {
  try {
    // Check if index files exist
    const indexPath = `${RNFS.DocumentDirectoryPath}/hnsw_rag`;
    const graphPath = `${indexPath}/documents_hnsw_rs.hnsw.graph`;
    const dataPath = `${indexPath}/documents_hnsw_rs.hnsw.data`;
    const metadataPath = `${indexPath}/documents_metadata.json`;

    const graphExists = await RNFS.exists(graphPath);
    const dataExists = await RNFS.exists(dataPath);
    const metadataExists = await RNFS.exists(metadataPath);

    if (!graphExists || !dataExists || !metadataExists) {
      Alert.alert("Missing Files", "HNSW index files (.graph, .data) or metadata not found. RAG features will be disabled.");
      setRagEnabled(false);
      return;
    }

    // Just verify the metadata file exists and get its stats
    const metadataStats = await RNFS.stat(metadataPath);
    console.log(`✅ RAG system ready! Metadata file: ${(metadataStats.size / 1024 / 1024).toFixed(2)}MB`);
    console.log("📄 Documents will be loaded on-demand during search");

    // Set an empty documents array - we'll load documents lazily
    setDocuments([]);

    console.log("🚀 RAG system initialized with lazy loading");
  } catch (error) {
    console.error("Failed to load HNSW index:", error);
    Alert.alert(
      "RAG System Error",
      `Failed to initialize the RAG system. You can:\n\n1. Disable RAG using the toggle and use the chat normally\n2. Try restarting the app\n3. Check that all RAG files are present\n\nError: ${error.message}`,
      [
        { text: "Keep RAG Enabled", style: "default" },
        { text: "Disable RAG", onPress: () => setRagEnabled(false), style: "destructive" }
      ]
    );
  } finally {
    setIsLoadingRAG(false);
  }
};

// Tokenize text using loaded tokenizer (local or bundled)
const tokenizeText = async (text: string, maxLength: number = 512) => {
  if (tokenizer && tokenizer.isLoaded) {
    try {
      // Use the loaded tokenizer (local or bundled)
      const encoded = await tokenizer.tokenize(text, maxLength);

      const tokenizerType = tokenizer.constructor.name;
      console.log(`🔤 ${tokenizerType} tokenization successful:`, {
        inputIdsLength: encoded.input_ids.length,
        attentionMaskLength: encoded.attention_mask.length,
        firstFewTokens: encoded.input_ids.slice(0, 10),
        firstFewTokenNames: encoded.tokens ? encoded.tokens.slice(0, 10) : 'N/A'
      });

      return {
        input_ids: encoded.input_ids,
        attention_mask: encoded.attention_mask
      };
    } catch (error) {
      console.error("Tokenizer error:", error);
      console.log("Falling back to basic tokenizer");
      // Fall through to basic tokenizer
    }
  }

  // Enhanced fallback tokenizer for better Python script compatibility
  console.log("⚠️ Using enhanced fallback tokenizer for React Native");

  // More sophisticated text preprocessing similar to BERT tokenizers
  const cleanText = text.toLowerCase().trim();

  // Basic word-piece style tokenization
  const processedText = cleanText
    .replace(/[^\w\s]/g, ' ') // Replace punctuation with spaces
    .replace(/\s+/g, ' ')     // Normalize whitespace
    .trim();

  const words = processedText.split(' ').filter(word => word.length > 0);
  const tokens: number[] = [101]; // [CLS] token

  // Create more consistent token IDs using a simple but deterministic approach
  for (const word of words.slice(0, maxLength - 2)) {
    // Use a more stable hash function for better consistency
    let hash = 0;
    for (let i = 0; i < word.length; i++) {
      const char = word.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Map to a reasonable token range (similar to BERT vocab size)
    const tokenId = 1000 + Math.abs(hash % 28000); // Range: 1000-29000
    tokens.push(tokenId);
  }

  tokens.push(102); // [SEP] token

  // Pad to max length
  while (tokens.length < maxLength) {
    tokens.push(0); // [PAD] token
  }

  const finalTokens = tokens.slice(0, maxLength);
  const attentionMask = finalTokens.map(id => id === 0 ? 0 : 1);

  return {
    input_ids: finalTokens,
    attention_mask: attentionMask
  };
};

// Embed text using the ONNX model (matches Python script exactly)
const embedText = async (text: string) => {
  if (!embeddingSession) {
    console.error("Embedding session not initialized");
    return null;
  }

  try {
    console.log("Starting embedding for text:", text.substring(0, 50) + "...");

    // Use proper HuggingFace tokenization
    const tokenized = await tokenizeText(text, 512);
    console.log(`Tokenized to ${tokenized.input_ids.length} tokens`);

    // Create ONNX tensors - this matches your Python script exactly
    const feeds = {
      'input_ids': new ort.Tensor('int64', tokenized.input_ids, [1, tokenized.input_ids.length]),
      'attention_mask': new ort.Tensor('int64', tokenized.attention_mask, [1, tokenized.attention_mask.length])
    };

    console.log("Prepared feeds for ONNX model:", Object.keys(feeds));
    console.log("Input IDs shape:", feeds['input_ids'].dims);
    console.log("First few input IDs:", tokenized.input_ids.slice(0, 10));

    console.log("Running ONNX inference...");
    const results = await embeddingSession.run(feeds);
    console.log("ONNX results keys:", Object.keys(results));

    // Try common output key names for sentence transformers
    const possibleOutputKeys = ['last_hidden_state', 'output', 'pooler_output', 'sentence_embedding', '0'];
    let embedding: Float32Array | null = null;
    let usedKey = '';

    for (const key of possibleOutputKeys) {
      if (results[key] && results[key].data && results[key].data.length > 0) {
        embedding = results[key].data as Float32Array;
        usedKey = key;
        console.log(`Using '${key}' as embedding output. Shape:`, results[key].dims, "Length:", embedding.length);
        break;
      }
    }

    // If no common keys found, try any available key
    if (!embedding) {
      const availableKeys = Object.keys(results).filter(key =>
        results[key] && results[key].data && results[key].data.length > 0);

      if (availableKeys.length > 0) {
        usedKey = availableKeys[0];
        embedding = results[usedKey].data as Float32Array;
        console.log(`Fallback: using '${usedKey}' as embedding output. Shape:`,
          results[usedKey].dims, "Length:", embedding.length);
      }
    }

    if (!embedding || embedding.length === 0) {
      console.warn("No suitable embedding output found in ONNX results");
      console.warn("Available keys:", Object.keys(results));
      return null;
    }

    // Apply mean pooling exactly like your Python script
    let finalEmbedding = embedding;

    // Check if we need to do mean pooling (3D output)
    const outputShape = results[usedKey].dims;
    if (outputShape.length === 3) {
      const [batch, seqLen, hiddenSize] = outputShape;
      console.log(`Detected 3D output [${batch}, ${seqLen}, ${hiddenSize}], performing mean pooling like Python script`);

      // Mean pooling across sequence dimension (matches Python: np.mean(normalized, axis=0))
      const pooled = new Float32Array(hiddenSize);
      for (let i = 0; i < hiddenSize; i++) {
        let sum = 0;
        let count = 0;
        for (let j = 0; j < seqLen; j++) {
          const idx = j * hiddenSize + i;
          if (tokenized.attention_mask[j] === 1) { // Only pool non-padded tokens
            sum += embedding[idx];
            count++;
          }
        }
        pooled[i] = count > 0 ? sum / count : 0;
      }
      finalEmbedding = pooled;
      console.log("Mean pooling completed. Pooled embedding length:", finalEmbedding.length);
    }

    // Normalize the embedding for cosine similarity (matches Python script)
    const norm = Math.sqrt(Array.from(finalEmbedding).reduce((sum, val) => sum + val * val, 0));
    if (norm === 0) {
      console.warn("Embedding norm is zero, cannot normalize");
      return null;
    }

    const normalizedEmbedding = new Float32Array(Array.from(finalEmbedding).map(val => val / norm));
    const tokenizerType = tokenizer && tokenizer.isLoaded ? tokenizer.constructor.name : 'Fallback';
    console.log("✅ Embedding created (Python-compatible tokenizer):", {
      length: normalizedEmbedding.length,
      norm: 1.0, // Should be 1.0 after normalization
      firstFew: Array.from(normalizedEmbedding).slice(0, 5),
      tokenizerUsed: tokenizerType
    });

    return normalizedEmbedding;
  } catch (error) {
    console.error("Error embedding text:", error);
    // Log more details about the error
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Stack trace:", error.stack);
    }
    return null;
  }
}

  // Query the HNSW index for similar documents
  const querySimilarDocuments = async (query: string, k: number = 5) => {
    if (!ragEnabled) {
      return [];
    }

    try {
      console.log("🔍 Starting RAG query for:", query);

      const embedding = await embedText(query);
      if (!embedding) {
        console.warn("Failed to generate embedding, skipping RAG");
        return [];
      }

      console.log("📊 Generated embedding:", {
        length: embedding.length,
        firstFew: Array.from(embedding).slice(0, 5),
        norm: Math.sqrt(Array.from(embedding).reduce((sum, val) => sum + val * val, 0))
      });

      // Check if native module is available
      if (!HnswSearchModule) {
        console.warn("HnswSearchModule not available, RAG search disabled");
        return [];
      }

      // Use the native module for search
      const indexPath = `${RNFS.DocumentDirectoryPath}/hnsw_rag`;
      const results = await HnswSearchModule.searchKnn(
        Array.from(embedding), // Convert TypedArray to regular array
        embedding.length,     // Dimension
        k,                    // Number of neighbors
        indexPath,            // Directory path
        "documents_hnsw_rs"      // Base filename (without extension)
      );

      console.log(`🔍 HNSW search returned ${results.neighbors.length} document indices:`, results.neighbors);
      console.log(`📏 Search distances:`, results.distances || 'No distances returned');

      // Load documents lazily using the returned indices
      const retrievedDocs = [];
      for (let i = 0; i < results.neighbors.length; i++) {
        const idx = results.neighbors[i];
        const distance = results.distances ? results.distances[i] : 'unknown';

        console.log(`📄 Loading document ${idx} (distance: ${distance})...`);
        const doc = await loadDocumentByIndex(idx);
        if (doc) {
          retrievedDocs.push(doc);
          console.log(`✅ Loaded document ${idx}:`, {
            source: doc.metadata?.source || 'unknown source',
            contentPreview: doc.content.substring(0, 100) + '...',
            contentLength: doc.content.length,
            distance: distance
          });
        } else {
          console.warn(`❌ Failed to load document ${idx}`);
          // Create a placeholder document so the user knows something was found
          retrievedDocs.push({
            id: idx,
            content: `Document ${idx} could not be loaded from the knowledge base.`,
            metadata: { source: `document_${idx}`, type: "error" }
          });
        }
      }

      console.log(`🎯 RAG retrieved ${retrievedDocs.length} documents with lazy loading`);

      // Log relevance analysis
      console.log("🔍 Relevance Analysis:");
      retrievedDocs.forEach((doc, i) => {
        const queryLower = query.toLowerCase();
        const contentLower = doc.content.toLowerCase();
        const hasQueryTerms = queryLower.split(' ').some(term =>
          term.length > 2 && contentLower.includes(term)
        );
        console.log(`  Document ${i + 1} (${doc.metadata?.source}): ${hasQueryTerms ? '✅ Contains query terms' : '❌ No query terms found'}`);
      });

      return retrievedDocs;
    } catch (error) {
      console.error("Error querying similar documents:", error);
      // Don't fail the entire query if RAG fails
      return [];
    }
  };
  

  // Format the retrieved documents into a context string
  const formatRetrievedContext = (retrievedDocs: Document[]) => {
    if (!retrievedDocs || retrievedDocs.length === 0) {
      return "";
    }

    return retrievedDocs.map(doc => {
      return `[Source: ${doc.metadata.source}]\n${doc.content.substring(0, 1000)}${doc.content.length > 1000 ? '...' : ''}`;
    }).join('\n\n---\n\n');
  };

  const handleGGUFSelection = (file: string) => {
    setSelectedGGUF(file);
    Alert.alert(
      "Confirm Download",
      `Do you want to download ${file} ?`,
      [
        {
          text: "No",
          onPress: () => setSelectedGGUF(null),
          style: "cancel",
        },
        { text: "Yes", onPress: () => handleDownloadAndNavigate(file) },
      ],
      { cancelable: false }
    );
  };

  const handleDownloadAndNavigate = async (file: string) => {
    await handleDownloadModel(file);
    setCurrentPage("conversation"); // Navigate to conversation after download
  };

  const handleBackToModelSelection = () => {
    setContext(null);
    releaseAllLlama();
    setConversation(INITIAL_CONVERSATION);
    setSelectedGGUF(null);
    setTokensPerSecond([]);
    setCurrentPage("modelSelection");
  };

  const toggleThought = (messageIndex: number) => {
    setConversation((prev) =>
      prev.map((msg, index) =>
        index === messageIndex ? { ...msg, showThought: !msg.showThought } : msg
      )
    );
  };

  const fetchAvailableGGUFs = async (modelFormat: string) => {
    setIsFetching(true);
    console.log(HF_TO_GGUF[modelFormat as keyof typeof HF_TO_GGUF]);
    try {
      const response = await axios.get(
        `https://huggingface.co/api/models/${
          HF_TO_GGUF[modelFormat as keyof typeof HF_TO_GGUF]
        }`
      );
      console.log(response);
      const files = response.data.siblings.filter((file: any) =>
        file.rfilename.endsWith(".gguf")
      );
      setAvailableGGUFs(files.map((file: any) => file.rfilename));
    } catch (error) {
      Alert.alert(
        "Error",
        "Failed to fetch .gguf files from Hugging Face API."
      );
    } finally {
      setIsFetching(false);
    }
  };

  const handleFormatSelection = (format: string) => {
    setSelectedModelFormat(format);
    setAvailableGGUFs([]); // Clear any previous list
    fetchAvailableGGUFs(format); // Fetch .gguf files for selected format
  };

  const checkDownloadedModels = async () => {
    try {
      const files = await RNFS.readDir(RNFS.DocumentDirectoryPath);
      const ggufFiles = files
        .filter((file) => file.name.endsWith(".gguf"))
        .map((file) => file.name);
      setDownloadedModels(ggufFiles);
    } catch (error) {
      console.error("Error checking downloaded models:", error);
    }
  };



  useEffect(() => {
    checkDownloadedModels();
  }, [currentPage]);

  const checkFileExists = async (filePath: string) => {
    try {
      const fileExists = await RNFS.exists(filePath);
      console.log("File exists:", fileExists);
      return fileExists;
    } catch (error) {
      console.error("Error checking file existence:", error);
      return false;
    }
  };

  const handleScroll = (event: any) => {
    const currentPosition = event.nativeEvent.contentOffset.y;
    const contentHeight = event.nativeEvent.contentSize.height;
    const scrollViewHeight = event.nativeEvent.layoutMeasurement.height;

    // Store current scroll position and content height
    scrollPositionRef.current = currentPosition;
    contentHeightRef.current = contentHeight;

    // If user has scrolled up more than 100px from bottom, disable auto-scroll
    const distanceFromBottom =
      contentHeight - scrollViewHeight - currentPosition;
    setAutoScrollEnabled(distanceFromBottom < 100);
  };

  const handleDownloadModel = async (file: string) => {
    const downloadUrl = `https://huggingface.co/${
      HF_TO_GGUF[selectedModelFormat as keyof typeof HF_TO_GGUF]
    }/resolve/main/${file}`;
    setIsDownloading(true);
    setProgress(0);

    const destPath = `${RNFS.DocumentDirectoryPath}/${file}`;
    if (await checkFileExists(destPath)) {
      const success = await loadModel(file);
      if (success) {
        Alert.alert(
          "Info",
          `File ${destPath} already exists, we will load it directly.`
        );
        setIsDownloading(false);
        return;
      }
    }
    try {
      console.log("before download");
      console.log(isDownloading);

      const destPath = await downloadModel(file, downloadUrl, (progress) =>
        setProgress(progress)
      );
      Alert.alert("Success", `Model downloaded to: ${destPath}`);

      // After downloading, load the model
      await loadModel(file);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      Alert.alert("Error", `Download failed: ${errorMessage}`);
    } finally {
      setIsDownloading(false);
    }
  };

  const stopGeneration = async () => {
    try {
      await context.stopCompletion();
      setIsGenerating(false);
      setIsLoading(false);

      setConversation((prev) => {
        const lastMessage = prev[prev.length - 1];
        if (lastMessage.role === "assistant") {
          return [
            ...prev.slice(0, -1),
            {
              ...lastMessage,
              content: lastMessage.content + "\n\n*Generation stopped by user*",
            },
          ];
        }
        return prev;
      });
    } catch (error) {
      console.error("Error stopping completion:", error);
    }
  };

  const loadModel = async (modelName: string) => {
    try {
      const destPath = `${RNFS.DocumentDirectoryPath}/${modelName}`;
      console.log("destPath : ", destPath);
      if (context) {
        await releaseAllLlama();
        setContext(null);
        setConversation(INITIAL_CONVERSATION);
      }
      const llamaContext = await initLlama({
        model: destPath,
        use_mlock: true,
        n_ctx: 2048,
        n_gpu_layers: 1,
      });
      setContext(llamaContext);
      Alert.alert("Model Loaded", "The model was successfully loaded.");
      return true;
    } catch (error) {
      console.log("error : ", error);
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      Alert.alert("Error Loading Model", errorMessage);
      return false;
    }
  };

  const handleSendMessage = async () => {
    if (!context) {
      Alert.alert("Model Not Loaded", "Please load the model first.");
      return;
    }
    if (!userInput.trim()) {
      Alert.alert("Input Error", "Please enter a message.");
      return;
    }

    const newConversation: Message[] = [
      ...conversation,
      { role: "user", content: userInput },
    ];
    setConversation(newConversation);
    setUserInput("");
    setIsLoading(true);
    setIsGenerating(true);
    setAutoScrollEnabled(true);

    try {
      // RAG retrieval with improved error handling
      let retrievedContext = "";
      if (ragEnabled) {
        try {
          console.log("Attempting RAG retrieval...");
          const retrievedDocs = await querySimilarDocuments(userInput);
          retrievedContext = formatRetrievedContext(retrievedDocs);
          console.log("Retrieved context:", retrievedContext ? "Yes" : "No");
          if (retrievedContext) {
            console.log("RAG retrieval successful");
          } else {
            console.log("RAG retrieval returned no context");
          }
        } catch (ragError) {
          console.error("RAG retrieval failed, continuing without context:", ragError);
          // Don't fail the entire conversation if RAG fails
          retrievedContext = "";
        }
      }

      const stopWords = [
        "</s>",
        "<|end|>",
        "user:",
        "assistant:",
        "<|im_end|>",
        "<|eot_id|>",
        "<|end▁of▁sentence|>",
        "<|end_of_text|>",
        "<｜end▁of▁sentence｜>",
      ];
      const chat = newConversation;

      // Modify system message with RAG context if available
      if (retrievedContext && ragEnabled) {
        // Create a new array with the updated system message
        const chatWithContext = [
          {
            role: "system",
            content: `This is a conversation between user and assistant, a friendly chatbot. Use the following retrieved information to help answer the user's question, but only when it's relevant. If the information doesn't help, rely on your own knowledge.\n\nRetrieved Information:\n${retrievedContext}`,
          },
          ...chat.slice(1) // Skip the original system message
        ];
        chat.splice(0, 1, chatWithContext[0]); // Replace system message
      }

      // Append a placeholder for the assistant's response
      setConversation((prev) => [
        ...prev,
        {
          role: "assistant",
          content: "",
          thought: undefined,
          showThought: false,
          context: retrievedContext,
          showContext: false,
        },
      ]);

      let currentAssistantMessage = "";
      let currentThought = "";
      let inThinkBlock = false;
      interface CompletionData {
        token: string;
      }

      interface CompletionResult {
        timings: {
          predicted_per_second: number;
        };
      }

      const result: CompletionResult = await context.completion(
        {
          messages: chat,
          n_predict: 10000,
          stop: stopWords,
        },
        (data: CompletionData) => {
          const token = data.token; // Extract the token
          currentAssistantMessage += token; // Append token to the current message

          if (token.includes("<think>")) {
            inThinkBlock = true;
            currentThought = token.replace("<think>", "");
          } else if (token.includes("</think>")) {
            inThinkBlock = false;
            const finalThought = currentThought.replace("</think>", "").trim();

            setConversation((prev) => {
              const lastIndex = prev.length - 1;
              const updated = [...prev];

              updated[lastIndex] = {
                ...updated[lastIndex],
                content: updated[lastIndex].content.replace(
                  `<think>${finalThought}</think>`,
                  ""
                ),
                thought: finalThought,
              };

              return updated;
            });

            currentThought = "";
          } else if (inThinkBlock) {
            currentThought += token;
          }

          const visibleContent = currentAssistantMessage
            .replace(/<think>.*?<\/think>/gs, "")
            .trim();

          setConversation((prev) => {
            const lastIndex = prev.length - 1;
            const updated = [...prev];
            updated[lastIndex].content = visibleContent;
            return updated;
          });

          if (autoScrollEnabled && scrollViewRef.current) {
            requestAnimationFrame(() => {
              scrollViewRef.current?.scrollToEnd({ animated: false });
            });
          }
        }
      );

      setTokensPerSecond((prev) => [
        ...prev,
        parseFloat(result.timings.predicted_per_second.toFixed(2)),
      ]);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      Alert.alert("Error During Inference", errorMessage);
    } finally {
      setIsLoading(false);
      setIsGenerating(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <ScrollView
          style={styles.scrollView}
          ref={scrollViewRef}
          onScroll={handleScroll}
          scrollEventThrottle={16}
        >
          <Text style={styles.title}>RAG-Enhanced Llama Chat</Text>
          {currentPage === "modelSelection" && !isDownloading && (
            <View style={styles.card}>
              <Text style={styles.subtitle}>Choose a model format</Text>
              {modelFormats.map((format) => (
                <TouchableOpacity
                  key={format.label}
                  style={[
                    styles.button,
                    selectedModelFormat === format.label &&
                      styles.selectedButton,
                  ]}
                  onPress={() => handleFormatSelection(format.label)}
                >
                  <Text style={styles.buttonText}>{format.label}</Text>
                </TouchableOpacity>
              ))}
              {selectedModelFormat && (
                <View>
                  <Text style={styles.subtitle}>Select a .gguf file</Text>
                  {isFetching && (
                    <ActivityIndicator size="small" color="#2563EB" />
                  )}
                  {availableGGUFs.map((file, index) => {
                    const isDownloaded = downloadedModels.includes(file);
                    return (
                      <View key={index} style={styles.modelContainer}>
                        <TouchableOpacity
                          style={[
                            styles.modelButton,
                            selectedGGUF === file && styles.selectedButton,
                            isDownloaded && styles.downloadedModelButton,
                          ]}
                          onPress={() =>
                            isDownloaded
                              ? (loadModel(file),
                                setCurrentPage("conversation"),
                                setSelectedGGUF(file))
                              : handleGGUFSelection(file)
                          }
                        >
                          <View style={styles.modelButtonContent}>
                            <View style={styles.modelStatusContainer}>
                              {isDownloaded ? (
                                <View style={styles.downloadedIndicator}>
                                  <Text style={styles.downloadedIcon}>▼</Text>
                                </View>
                              ) : (
                                <View style={styles.notDownloadedIndicator}>
                                  <Text style={styles.notDownloadedIcon}>
                                    ▽
                                  </Text>
                                </View>
                              )}
                              <Text
                                style={[
                                  styles.buttonTextGGUF,
                                  selectedGGUF === file &&
                                    styles.selectedButtonText,
                                  isDownloaded && styles.downloadedText,
                                ]}
                              >
                                {file.split("-")[-1] === "imat"
                                  ? file
                                  : file.split("-").pop()}
                              </Text>
                            </View>
                            {isDownloaded && (
                              <View style={styles.loadModelIndicator}>
                                <Text style={styles.loadModelText}>
                                  TAP TO LOAD →
                                </Text>
                              </View>
                            )}
                            {!isDownloaded && (
                              <View style={styles.downloadIndicator}>
                                <Text style={styles.downloadText}>
                                  DOWNLOAD →
                                </Text>
                              </View>
                            )}
                          </View>
                        </TouchableOpacity>
                      </View>
                    );
                  })}
                </View>
              )}
            </View>
          )}
          {currentPage === "conversation" && !isDownloading && (
            <View style={styles.chatWrapper}>
              <View style={styles.headerRow}>
                <Text style={styles.subtitle2}>Chatting with {selectedGGUF}</Text>
                <View style={styles.ragToggleContainer}>
                  <Text style={styles.ragLabel}>RAG</Text>
                  <Switch
                    value={ragEnabled}
                    onValueChange={toggleRAG}
                    trackColor={{ false: "#767577", true: "#3B82F6" }}
                    thumbColor={ragEnabled ? "#FFFFFF" : "#f4f3f4"}
                  />
                </View>
              </View>
              <View style={styles.chatContainer}>
                <Text style={styles.greetingText}>
                  🦙 Welcome! The Llama is ready to chat{ragEnabled ? ' with RAG enhancement' : ''}. Ask away! 🎉
                </Text>
                {conversation.slice(1).map((msg, index) => (
                  <View key={index} style={styles.messageWrapper}>
                    <View
                      style={[
                        styles.messageBubble,
                        msg.role === "user"
                          ? styles.userBubble
                          : styles.llamaBubble,
                      ]}
                    >
                      <View>
                        {/* Context Toggle Button */}
                        {msg.context && (
                          <TouchableOpacity
                            onPress={() => toggleContext(index + 1)}
                            style={styles.toggleButton}
                          >
                            <Text style={styles.ragToggleText}>
                              {msg.showContext
                                ? "▼ Hide Retrieved Context"
                                : "▶ Show Retrieved Context"}
                            </Text>
                          </TouchableOpacity>
                        )}

                        {/* Display Context if enabled */}
                        {msg.showContext && msg.context && (
                          <View style={styles.contextContainer}>
                            <Text style={styles.contextTitle}>
                              Retrieved Knowledge:
                            </Text>
                            <Text style={styles.contextText}>
                              {msg.context}
                            </Text>
                          </View>
                        )}
                        {/* Thought Toggle Button */}
                        {msg.thought && (
                          <TouchableOpacity
                            onPress={() => toggleThought(index + 1)}
                            style={styles.toggleButton}
                          >
                            <Text style={styles.toggleText}>
                              {msg.showThought
                                ? "▼ Hide Thought"
                                : "▶ Show Thought"}
                            </Text>
                          </TouchableOpacity>
                        )}
                        {msg.showThought && msg.thought && (
                          <View style={styles.thoughtContainer}>
                            <Text style={styles.thoughtTitle}>
                              Model's Reasoning:
                            </Text>
                            <Text style={styles.thoughtText}>
                              {msg.thought}
                            </Text>
                          </View>
                        )}
                        <Markdown
                          style={{
                            body: {
                              color: msg.role === "user" ? "#FFFFFF" : "#334155",
                              fontSize: 16,
                            },
                            paragraph: {
                              color: msg.role === "user" ? "#FFFFFF" : "#334155",
                              fontSize: 16,
                              marginTop: 0,
                              marginBottom: 8,
                            },
                            text: {
                              color: msg.role === "user" ? "#FFFFFF" : "#334155",
                              fontSize: 16,
                            }
                          }}
                        >
                          {msg.content}
                        </Markdown>
                      </View>
                    </View>
                    {msg.role === "assistant" && (
                      <Text
                        style={styles.tokenInfo}
                      >
                        {tokensPerSecond[Math.floor(index / 2)]} tokens/s
                        {msg.context ? " · RAG-enhanced" : ""}
                      </Text>
                    )}
                  </View>
                ))}
              </View>
            </View>
          )}
          {isDownloading && (
            <View style={styles.card}>
              <Text style={styles.subtitle}>Downloading : </Text>
              <Text style={styles.subtitle2}>{selectedGGUF}</Text>
              <ProgressBar progress={progress} />
            </View>
          )}
          {isLoadingRAG && (
            <View style={styles.ragLoadingCard}>
              <Text style={styles.subtitle}>Initializing RAG system...</Text>
              <ActivityIndicator size="large" color="#2563EB" />
            </View>
          )}
        </ScrollView>
        <View style={styles.bottomContainer}>
          {currentPage === "conversation" && (
            <>
              <View style={styles.inputContainer}>
                <View style={styles.inputRow}>
                  <TextInput
                    style={styles.input}
                    placeholder="Type your message..."
                    placeholderTextColor="#94A3B8"
                    value={userInput}
                    onChangeText={setUserInput}
                  />
                  {isGenerating ? (
                    <TouchableOpacity
                      style={styles.stopButton}
                      onPress={stopGeneration}
                    >
                      <Text style={styles.buttonText}>□ Stop</Text>
                    </TouchableOpacity>
                  ) : (
                    <TouchableOpacity
                      style={styles.sendButton}
                      onPress={handleSendMessage}
                      disabled={isLoading}
                    >
                      <Text style={styles.buttonText}>
                        {isLoading ? "Sending..." : "Send"}
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
              <TouchableOpacity
                style={styles.backButton}
                onPress={handleBackToModelSelection}
              >
                <Text style={styles.backButtonText}>
                  ← Back to Model Selection
                </Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  scrollView: {
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: "700",
    color: "#1E293B",
    marginVertical: 24,
    textAlign: "center",
  },
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 24,
    margin: 16,
    shadowColor: "#475569",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#334155",
    marginBottom: 16,
    marginTop: 16,
  },
  subtitle2: {
    fontSize: 12,
    fontWeight: "600",
    marginBottom: 16,
    color: "#93C5FD",
  },
  button: {
    backgroundColor: "#93C5FD", // Lighter blue
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginVertical: 6,
    shadowColor: "#93C5FD", // Matching lighter shadow color
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15, // Slightly reduced opacity for subtle shadows
    shadowRadius: 4,
    elevation: 2,
  },
  selectedButton: {
    backgroundColor: "#2563EB",
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center",
  },
  chatWrapper: {
    flex: 1,
    padding: 16,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  ragToggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ragLabel: {
    marginRight: 8,
    fontSize: 14,
    fontWeight: '600',
    color: '#334155',
  },
  backButton: {
    backgroundColor: "#3B82F6",
    marginHorizontal: 16,
    marginTop: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: "center",
  },
  backButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  chatContainer: {
    flex: 1,
    backgroundColor: "#F8FAFC",
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  messageWrapper: {
    marginBottom: 16,
  },
  messageBubble: {
    padding: 12,
    borderRadius: 12,
    maxWidth: "80%",
  },
  userBubble: {
    alignSelf: "flex-end",
    backgroundColor: "#3B82F6",
  },
  llamaBubble: {
    alignSelf: "flex-start",
    backgroundColor: "#FFFFFF",
    borderWidth: 1,
    borderColor: "#E2E8F0",
  },
  messageText: {
    fontSize: 16,
    color: "#334155",
  },
  userMessageText: {
    color: "#FFFFFF",
  },
  tokenInfo: {
    fontSize: 12,
    color: "#94A3B8",
    marginTop: 4,
    textAlign: "right",
  },
  inputContainer: {
    padding: 16,
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: "#E2E8F0",
  },
  input: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    borderWidth: 1,
    borderColor: "#E2E8F0",
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: "#334155",
    minHeight: 50,
  },
  inputRow: {
    flexDirection: "row",
    gap: 12,
  },
  sendButton: {
    backgroundColor: "#3B82F6",
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    shadowColor: "#3B82F6",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
    alignSelf: "stretch",
    justifyContent: "center",
  },

  stopButton: {
    backgroundColor: "#FF3B30",
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignSelf: "stretch",
    justifyContent: "center",
  },
  greetingText: {
    fontSize: 12,
    fontWeight: "500",
    textAlign: "center",
    marginVertical: 12,
    color: "#64748B", // Soft gray that complements #2563EB
  },
  thoughtContainer: {
    marginTop: 8,
    padding: 10,
    backgroundColor: "#F1F5F9",
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: "#94A3B8",
  },
  thoughtTitle: {
    color: "#64748B",
    fontSize: 12,
    fontWeight: "600",
    marginBottom: 4,
  },
  thoughtText: {
    color: "#475569",
    fontSize: 12,
    fontStyle: "italic",
    lineHeight: 16,
  },
  contextContainer: {
    marginTop: 8,
    marginBottom: 12,
    padding: 10,
    backgroundColor: "#EFF6FF",
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: "#3B82F6",
  },
  contextTitle: {
    color: "#3B82F6",
    fontSize: 12,
    fontWeight: "600",
    marginBottom: 4,
  },
  contextText: {
    color: "#334155",
    fontSize: 12,
    lineHeight: 16,
  },
  toggleButton: {
    marginTop: 8,
    paddingVertical: 4,
  },
  toggleText: {
    color: "#3B82F6",
    fontSize: 12,
    fontWeight: "500",
  },
  ragToggleText: {
    color: "#3B82F6",
    fontSize: 12,
    fontWeight: "500",
  },
  bottomContainer: {
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: "#E2E8F0",
    paddingBottom: Platform.OS === "ios" ? 20 : 10,
  },
  modelContainer: {
    marginVertical: 6,
    borderRadius: 12,
    overflow: "hidden",
  },
  modelButton: {
    backgroundColor: "#EFF6FF",
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#BFDBFE",
    shadowColor: "#3B82F6",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  downloadedModelButton: {
    backgroundColor: "#EFF6FF",
    borderColor: "#3B82F6",
    borderWidth: 1,
  },
  modelButtonContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  modelStatusContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  downloadedIndicator: {
    backgroundColor: "#DBEAFE",
    padding: 4,
    borderRadius: 6,
    marginRight: 8,
  },
  notDownloadedIndicator: {
    backgroundColor: "#F1F5F9",
    padding: 4,
    borderRadius: 6,
    marginRight: 8,
  },
  downloadedIcon: {
    color: "#3B82F6",
    fontSize: 14,
    fontWeight: "bold",
  },
  notDownloadedIcon: {
    color: "#94A3B8",
    fontSize: 14,
    fontWeight: "bold",
  },
  downloadedText: {
    color: "#1E40AF",
  },
  loadModelIndicator: {
    backgroundColor: "#DBEAFE",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 6,
    marginLeft: 8,
  },
  loadModelText: {
    color: "#3B82F6",
    fontSize: 8,
    fontWeight: "600",
    letterSpacing: 0.5,
  },
  downloadIndicator: {
    backgroundColor: "#DCF9E5", // Light green background
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 6,
    marginLeft: 8,
  },
  downloadText: {
    color: "#16A34A", // Green text
    fontSize: 8,
    fontWeight: "600",
    letterSpacing: 0.5,
  },
  buttonTextGGUF: {
    color: "#1E40AF",
    fontSize: 14,
    fontWeight: "500",
  },
  selectedButtonText: {
    color: "#FFFFFF",
    fontWeight: "600",
  },
  ragLoadingCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 24,
    margin: 16,
    shadowColor: "#475569",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    alignItems: "center",
  },
});

export default App;
