cmake_minimum_required(VERSION 3.13.0)

project(hnsw_native)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_library(log-lib log)

# Add the JNI bridge library
add_library(
        # Sets the name of the library.
        hnsw_native

        # Sets the library as a shared library.
        SHARED

        # Provides a relative path to your source file(s).
        hnsw_jni.cpp
)

# Link libraries
target_link_libraries(hnsw_native
    ${log-lib}
)

# Include directories
target_include_directories(hnsw_native PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
)
