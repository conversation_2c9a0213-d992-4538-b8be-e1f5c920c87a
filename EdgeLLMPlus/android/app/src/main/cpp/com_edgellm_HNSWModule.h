/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_edgellm_HNSWModule */

#ifndef _Included_com_edgellm_HNSWModule
#define _Included_com_edgellm_HNSWModule
#ifdef __cplusplus
extern "C" {
#endif

/*
 * Class:     com_edgellm_HNSWModule
 * Method:    searchKnn
 * Signature: ([FIIL java/lang/String;Ljava/lang/String;)[I
 */
JNIEXPORT jintArray JNICALL Java_com_edgellm_HNSWModule_searchKnn
  (JNIEnv *, jobject, jfloatArray, jint, jint, jstring, jstring);

#ifdef __cplusplus
}
#endif
#endif
