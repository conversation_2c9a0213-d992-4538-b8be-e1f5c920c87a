#include <jni.h>
#include <string>
#include <vector>
#include <android/log.h>
#include <dlfcn.h>

#define LOG_TAG "HNSW_JNI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// Function pointer type for the Rust function
typedef int (*hnsw_search_func)(
        const float* query_ptr,
        size_t dim,
        size_t k,
        int* out_ptr,
        const char* dir_ptr,
        const char* base_ptr
);

// Global handle for the Rust library
static void* rust_lib_handle = nullptr;
static hnsw_search_func hnsw_search_ptr = nullptr;
static bool attempted_load = false;

// Function to load the Rust library
bool load_rust_library() {
    if (attempted_load) {
        return (rust_lib_handle != nullptr && hnsw_search_ptr != nullptr);
    }

    attempted_load = true;

    LOGI("Attempting to load Rust library...");
    // Try different possible names based on your Cargo.toml name
    const char* lib_names[] = {
            "libhnsw_rs.so",           // If your crate is named "hnsw_rs"
            "libhnsw.so",              // If your crate is named "hnsw"
            "libedgellm.so",           // If your crate is named "edgellm"
            "libhnsw_native.so",       // Generic name
            "librust_hnsw.so"          // Alternative
    };

    for (const char* lib_name : lib_names) {
        rust_lib_handle = dlopen(lib_name, RTLD_LAZY);
        if (rust_lib_handle != nullptr) {
            LOGI("Successfully loaded %s", lib_name);
            break;
        }
        LOGI("Failed to load %s: %s", lib_name, dlerror());
    }

    if (rust_lib_handle == nullptr) {
        LOGE("Failed to load any Rust library variant");
        return false;
    }

    hnsw_search_ptr = (hnsw_search_func)dlsym(rust_lib_handle, "hnsw_search");
    if (hnsw_search_ptr == nullptr) {
        LOGE("Failed to find hnsw_search function: %s", dlerror());
        dlclose(rust_lib_handle);
        rust_lib_handle = nullptr;
        return false;
    }

    LOGI("Successfully loaded Rust library and function");
    return true;
}

extern "C" JNIEXPORT jintArray JNICALL
Java_com_edgellm_HNSWModule_searchKnn(JNIEnv *env, jobject thiz, jfloatArray query_array, jint dimension, jint k, jstring index_path, jstring base_name) {
    LOGI("searchKnn called with dimension=%d, k=%d", dimension, k);

    // Convert Java strings to C strings
    const char* index_path_str = env->GetStringUTFChars(index_path, nullptr);
    const char* base_name_str = env->GetStringUTFChars(base_name, nullptr);

    // Convert Java float array to C array
    jfloat* query_data = env->GetFloatArrayElements(query_array, nullptr);
    jsize query_length = env->GetArrayLength(query_array);

    LOGI("Query array length: %d, expected dimension: %d", query_length, dimension);
    LOGI("Index path: %s, base name: %s", index_path_str, base_name_str);

    jintArray result = nullptr;

    try {
        // Load the Rust library if not already loaded
        if (!load_rust_library()) {
            LOGE("Rust library not available - returning mock results");

            // Return mock results for testing
            std::vector<int> mock_results(k);
            for (int i = 0; i < k; i++) {
                mock_results[i] = i; // Mock indices
            }

            result = env->NewIntArray(k);
            if (result != nullptr) {
                env->SetIntArrayRegion(result, 0, k, mock_results.data());
                LOGI("Returned mock results with %d elements", k);
            }
        } else {
            // Prepare output buffer for results
            std::vector<int> results(k);

            // Call the Rust function
            int status = hnsw_search_ptr(
                    query_data,
                    static_cast<size_t>(dimension),
                    static_cast<size_t>(k),
                    results.data(),
                    index_path_str,
                    base_name_str
            );

            if (status != 0) {
                LOGE("hnsw_search failed with status: %d", status);
                jclass exceptionClass = env->FindClass("java/lang/RuntimeException");
                env->ThrowNew(exceptionClass, "Failed to search the HNSW index");
                return nullptr;
            }

            LOGI("hnsw_search succeeded, creating result array");

            // Create Java int array with the results
            result = env->NewIntArray(k);
            if (result != nullptr) {
                env->SetIntArrayRegion(result, 0, k, results.data());
                LOGI("Successfully created result array with %d elements", k);
            } else {
                LOGE("Failed to create result array");
            }
        }

    } catch (const std::exception& e) {
        LOGE("Exception in searchKnn: %s", e.what());
        jclass exceptionClass = env->FindClass("java/lang/RuntimeException");
        env->ThrowNew(exceptionClass, e.what());
    }

    // Clean up
    env->ReleaseFloatArrayElements(query_array, query_data, JNI_ABORT);
    env->ReleaseStringUTFChars(index_path, index_path_str);
    env->ReleaseStringUTFChars(base_name, base_name_str);

    return result;
}