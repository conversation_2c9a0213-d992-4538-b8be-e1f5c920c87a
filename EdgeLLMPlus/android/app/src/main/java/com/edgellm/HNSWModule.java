package com.edgellm;

import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.Arguments;

public class HNSWModule extends ReactContextBaseJavaModule {
    static {
        System.loadLibrary("hnsw_native");
    }

    public HNSWModule(ReactApplicationContext context) {
        super(context);
    }

    @Override
    public String getName() {
        return "HnswSearchModule";
    }

    // Native methods that interface with your Rust library
    private native int[] searchKnn(float[] query, int dimension, int k, String indexPath, String baseName);

    @ReactMethod
    public void searchKnn(ReadableArray queryArray, int dimension, int k, String indexPath, String baseName, Promise promise) {
        try {
            // Convert ReadableArray to float array
            float[] query = new float[queryArray.size()];
            for (int i = 0; i < queryArray.size(); i++) {
                query[i] = (float) queryArray.getDouble(i);
            }

            // Call native search function
            int[] results = searchKnn(query, dimension, k, indexPath, baseName);

            // Create result object matching JavaScript expectations
            WritableMap resultMap = Arguments.createMap();
            WritableArray neighborsArray = Arguments.createArray();
            WritableArray distancesArray = Arguments.createArray();

            // Assuming results contains alternating indices and distances
            // Adjust this based on your actual Rust implementation
            for (int i = 0; i < results.length; i += 2) {
                if (i + 1 < results.length) {
                    neighborsArray.pushInt(results[i]);
                    distancesArray.pushDouble(results[i + 1]);
                }
            }

            resultMap.putArray("neighbors", neighborsArray);
            resultMap.putArray("distances", distancesArray);

            promise.resolve(resultMap);
        } catch (Exception e) {
            promise.reject("SEARCH_ERROR", e.getMessage(), e);
        }
    }
}