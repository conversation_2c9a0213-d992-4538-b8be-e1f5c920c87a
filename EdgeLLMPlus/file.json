{"_id": "66616ddf3fdeeb338135dbef", "id": "Qwen/Qwen2-0.5B-Instruct-GGUF", "private": false, "pipeline_tag": "text-generation", "tags": ["gguf", "instruct", "chat", "text-generation", "en", "base_model:Qwen/Qwen2-0.5B-Instruct", "base_model:quantized:Qwen/Qwen2-0.5B-Instruct", "license:apache-2.0", "endpoints_compatible", "region:us", "conversational"], "downloads": 10297, "likes": 62, "modelId": "Qwen/Qwen2-0.5B-Instruct-GGUF", "author": "<PERSON><PERSON>", "sha": "198f08841147e5196a6a69bd0053690fb1fd3857", "lastModified": "2024-08-21T10:29:53.000Z", "gated": false, "inference": "library-not-detected", "disabled": false, "widgetData": [{"text": "Hi, what can you help me with?"}, {"text": "Hey, let's have a conversation!"}, {"text": "Hello there!"}, {"text": "Hey my name is <PERSON>! How are you?"}], "model-index": null, "cardData": {"language": ["en"], "pipeline_tag": "text-generation", "tags": ["instruct", "chat"], "license": "apache-2.0", "base_model": "Qwen/Qwen2-0.5B-Instruct"}, "gguf": {"total": 494032768, "architecture": "qwen2", "context_length": 32768, "chat_template": "{% for message in messages %}{% if loop.first and messages[0]['role'] != 'system' %}{{ '<|im_start|>system\nYou are a helpful assistant.<|im_end|>\n' }}{% endif %}{{'<|im_start|>' + message['role'] + '\n' + message['content'] + '<|im_end|>' + '\n'}}{% endfor %}{% if add_generation_prompt %}{{ '<|im_start|>assistant\n' }}{% endif %}", "bos_token": "<|endoftext|>", "eos_token": "<|im_end|>"}, "siblings": [{"rfilename": ".gitattributes"}, {"rfilename": "LICENSE"}, {"rfilename": "README.md"}, {"rfilename": "qwen2-0_5b-instruct-fp16.gguf"}, {"rfilename": "qwen2-0_5b-instruct-q2_k.gguf"}, {"rfilename": "qwen2-0_5b-instruct-q3_k_m.gguf"}, {"rfilename": "qwen2-0_5b-instruct-q4_0.gguf"}, {"rfilename": "qwen2-0_5b-instruct-q4_k_m.gguf"}, {"rfilename": "qwen2-0_5b-instruct-q5_0.gguf"}, {"rfilename": "qwen2-0_5b-instruct-q5_k_m.gguf"}, {"rfilename": "qwen2-0_5b-instruct-q6_k.gguf"}, {"rfilename": "qwen2-0_5b-instruct-q8_0.gguf"}], "spaces": ["xu-song/self-chat", "mitulagr2/whatsthispdf", "<PERSON><PERSON>/Voice-Assistant", "jianuo/LLMwatermark", "TobDeBer/Qwen-2-ll<PERSON><PERSON><PERSON>", "cheeku2/whatsthispdf00", "cheeku2/whatsthispdf01", "cheeku2/whatsthispdf02", "cheeku2/whatsthispdf03", "cheeku2/whatsthispdf04", "cheeku2/whatsthispdf05", "cheeku2/whatsthispdf06", "cheeku2/whatsthispdf07", "cheeku2/whatsthispdf08", "cheeku2/whatsthispdf09", "cheeku1/whatsthispdf00", "cheeku1/whatsthispdf01", "cheeku1/whatsthispdf02", "cheeku1/whatsthispdf03", "cheeku1/whatsthispdf04", "cheeku1/whatsthispdf05", "cheeku1/whatsthispdf06", "cheeku1/whatsthispdf07", "cheeku1/whatsthispdf08", "cheeku1/whatsthispdf09", "retereum/student-llm-guard", "uetuluk/student-llm-guard", "shanghai-rits/llmguard-demo"], "createdAt": "2024-06-06T08:05:51.000Z", "usedStorage": 9666088096}