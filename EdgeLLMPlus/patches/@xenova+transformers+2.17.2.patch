diff --git a/node_modules/@xenova/transformers/src/env.js b/node_modules/@xenova/transformers/src/env.js
index 92fb076..f12fc12 100644
--- a/node_modules/@xenova/transformers/src/env.js
+++ b/node_modules/@xenova/transformers/src/env.js
@@ -39,7 +39,7 @@ const PATH_AVAILABLE = !isEmpty(path); // check if path is available
 const RUNNING_LOCALLY = FS_AVAILABLE && PATH_AVAILABLE;
 
 const __dirname = RUNNING_LOCALLY
-    ? path.dirname(path.dirname(url.fileURLToPath(import.meta.url)))
+    ? path.dirname(path.dirname(url.fileURLToPath(typeof __filename !== 'undefined' ? __filename : '.')))
     : './';
 
 // Only used for environments with access to file system
