#!/bin/bash

# Set Android environment variables
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/emulator

# Check if ADB is available
if ! command -v adb &> /dev/null; then
    echo "Error: ADB not found. Please check your Android SDK installation."
    exit 1
fi

# Check device connection
echo "Checking connected devices..."
adb devices

# Run React Native Android
echo "Starting React Native Android build..."
npx react-native run-android
