{"rustc": 5357548097637079788, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2040997289075261528, "path": 8309896156705206472, "deps": [[9150530836556604396, "allocator_api2", false, 14630060198901900708], [10791833957791020630, "ahash", false, 12044872147604962288]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-ios-sim/release/.fingerprint/hashbrown-d5db7832864c0532/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 2602820287214250858}