{"rustc": 5357548097637079788, "features": "[\"alloc\", \"std\"]", "declared_features": "[\"alloc\", \"compiler_builtins\", \"core\", \"default\", \"libc\", \"logging\", \"rustc-dep-of-std\", \"std\", \"use_std\"]", "target": 11745930252914242013, "profile": 2040997289075261528, "path": 15470361672291965810, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-ios-sim/release/.fingerprint/memchr-c3e98c64b1e3ed65/dep-lib-memchr", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 2602820287214250858}