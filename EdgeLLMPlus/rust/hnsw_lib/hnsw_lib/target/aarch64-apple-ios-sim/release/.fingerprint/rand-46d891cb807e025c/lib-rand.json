{"rustc": 5357548097637079788, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2040997289075261528, "path": 15216873911590199585, "deps": [[1573238666360410412, "rand_chacha", false, 5302694019931295163], [2924422107542798392, "libc", false, 6856204846636243518], [18130209639506977569, "rand_core", false, 10619280802631765940]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-ios-sim/release/.fingerprint/rand-46d891cb807e025c/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 2602820287214250858}