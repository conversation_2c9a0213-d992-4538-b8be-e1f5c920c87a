{"rustc": 5357548097637079788, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2040997289075261528, "path": 15123246933266351031, "deps": [[555019317135488525, "regex_automata", false, 2489724388736416181], [2779309023524819297, "aho_corasick", false, 8388323731812799960], [3129130049864710036, "memchr", false, 17717500550492192397], [9408802513701742484, "regex_syntax", false, 7096728598970001680]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-ios-sim/release/.fingerprint/regex-e0f136fb9d40db52/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 2602820287214250858}