{"rustc": 13226066032359371072, "features": "[\"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 2040997289075261528, "path": 17074468635154126796, "deps": [[2924422107542798392, "libc", false, 7902460552728540248], [10411997081178400487, "cfg_if", false, 11282712259554256648]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/release/.fingerprint/getrandom-693e3fe3e5903a4c/dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 10416180240749053715, "compile_kind": 6373778340919622063}