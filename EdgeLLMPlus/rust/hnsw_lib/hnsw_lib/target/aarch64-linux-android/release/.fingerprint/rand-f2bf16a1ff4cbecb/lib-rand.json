{"rustc": 13226066032359371072, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2040997289075261528, "path": 12825378628608132617, "deps": [[1573238666360410412, "rand_chacha", false, 17067838231970596896], [2924422107542798392, "libc", false, 7902460552728540248], [18130209639506977569, "rand_core", false, 5728580749691059640]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/release/.fingerprint/rand-f2bf16a1ff4cbecb/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 10416180240749053715, "compile_kind": 6373778340919622063}