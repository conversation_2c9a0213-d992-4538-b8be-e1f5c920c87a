{"rustc": 13226066032359371072, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2040997289075261528, "path": 10778806019333069229, "deps": [[555019317135488525, "regex_automata", false, 3538374652941738758], [2779309023524819297, "aho_corasick", false, 5600486843418274383], [3129130049864710036, "memchr", false, 9046139953019078646], [9408802513701742484, "regex_syntax", false, 11535522028295210963]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/release/.fingerprint/regex-b0118cc92a9b2bed/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 10416180240749053715, "compile_kind": 6373778340919622063}