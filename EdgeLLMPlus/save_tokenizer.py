#!/usr/bin/env python3
"""
Script to save the HuggingFace tokenizer locally for React Native app usage.
This ensures exact consistency between Python scripts and React Native app.
"""

import os
import json
from transformers import AutoTokenizer

def save_tokenizer_for_react_native():
    """Save the tokenizer files that React Native can load"""
    
    # Load the same tokenizer used in your Python scripts
    model_name = 'sentence-transformers/all-MiniLM-L6-v2'
    print(f"Loading tokenizer: {model_name}")
    
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    
    # Create output directory
    output_dir = "tokenizer_files"
    os.makedirs(output_dir, exist_ok=True)
    
    # Save tokenizer files
    tokenizer.save_pretrained(output_dir)
    print(f"✅ Tokenizer saved to {output_dir}/")
    
    # Also save a simplified vocab mapping for React Native
    vocab = tokenizer.get_vocab()
    
    # Save the vocabulary as JSON for React Native
    vocab_file = os.path.join(output_dir, "vocab.json")
    with open(vocab_file, 'w', encoding='utf-8') as f:
        json.dump(vocab, f, ensure_ascii=False, indent=2)
    print(f"✅ Vocabulary saved to {vocab_file}")
    
    # Save tokenizer config for reference
    config = {
        "model_name": model_name,
        "vocab_size": len(vocab),
        "special_tokens": {
            "cls_token": tokenizer.cls_token,
            "sep_token": tokenizer.sep_token,
            "pad_token": tokenizer.pad_token,
            "unk_token": tokenizer.unk_token,
            "mask_token": tokenizer.mask_token if hasattr(tokenizer, 'mask_token') else None,
        },
        "special_token_ids": {
            "cls_token_id": tokenizer.cls_token_id,
            "sep_token_id": tokenizer.sep_token_id,
            "pad_token_id": tokenizer.pad_token_id,
            "unk_token_id": tokenizer.unk_token_id,
            "mask_token_id": tokenizer.mask_token_id if hasattr(tokenizer, 'mask_token_id') else None,
        },
        "max_length": tokenizer.model_max_length,
    }
    
    config_file = os.path.join(output_dir, "tokenizer_config_rn.json")
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2)
    print(f"✅ Config saved to {config_file}")
    
    # Test tokenization to verify
    test_text = "Hello world, this is a test."
    tokens = tokenizer(test_text, padding=True, truncation=True, max_length=512, return_tensors='pt')
    
    print(f"\n🧪 Test tokenization:")
    print(f"Text: {test_text}")
    print(f"Token IDs: {tokens['input_ids'][0][:10].tolist()}...")
    print(f"Attention mask: {tokens['attention_mask'][0][:10].tolist()}...")
    
    # Save test example for verification
    test_example = {
        "text": test_text,
        "input_ids": tokens['input_ids'][0].tolist(),
        "attention_mask": tokens['attention_mask'][0].tolist()
    }
    
    test_file = os.path.join(output_dir, "test_example.json")
    with open(test_file, 'w') as f:
        json.dump(test_example, f, indent=2)
    print(f"✅ Test example saved to {test_file}")
    
    print(f"\n📁 Files created in {output_dir}/:")
    for file in os.listdir(output_dir):
        file_path = os.path.join(output_dir, file)
        size = os.path.getsize(file_path)
        print(f"  - {file} ({size:,} bytes)")

if __name__ == "__main__":
    save_tokenizer_for_react_native()
